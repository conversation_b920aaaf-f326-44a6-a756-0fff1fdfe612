<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\VotingCenterController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Voting Centers API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('voting-centers')->group(function () {
    // Rutas públicas (sin autenticación)
    Route::get('/', [VotingCenterController::class, 'index']);
    Route::get('/search', [VotingCenterController::class, 'searchByCode']);
    Route::get('/statistics', [VotingCenterController::class, 'statistics']);
    Route::get('/export', [VotingCenterController::class, 'export']);
    
    // Rutas por ubicación
    Route::get('/state/{state}', [VotingCenterController::class, 'byState']);
    Route::get('/municipality/{municipality}', [VotingCenterController::class, 'byMunicipality']);
    Route::get('/parish/{parish}', [VotingCenterController::class, 'byParish']);
    
    // Rutas específicas
    Route::get('/{votingCenter}', [VotingCenterController::class, 'show']);
    
    // Rutas protegidas (requieren autenticación)
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/', [VotingCenterController::class, 'store']);
        Route::put('/{votingCenter}', [VotingCenterController::class, 'update']);
        Route::delete('/{votingCenter}', [VotingCenterController::class, 'destroy']);
    });
});

/*
|--------------------------------------------------------------------------
| Location API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('locations')->group(function () {
    // Estados
    Route::get('/states', function () {
        return response()->json([
            'success' => true,
            'data' => \App\Models\State::orderBy('name')->get(),
            'message' => 'Estados obtenidos exitosamente'
        ]);
    });
    
    // Municipios por estado
    Route::get('/states/{state}/municipalities', function (\App\Models\State $state) {
        return response()->json([
            'success' => true,
            'data' => $state->municipalities()->orderBy('name')->get(),
            'message' => "Municipios del estado {$state->name}"
        ]);
    });
    
    // Parroquias por municipio
    Route::get('/municipalities/{municipality}/parishes', function (\App\Models\Municipality $municipality) {
        return response()->json([
            'success' => true,
            'data' => $municipality->parishes()->orderBy('name')->get(),
            'message' => "Parroquias del municipio {$municipality->name}"
        ]);
    });
    
    // Todas las ubicaciones jerárquicas
    Route::get('/hierarchy', function () {
        $states = \App\Models\State::with(['municipalities.parishes'])
            ->orderBy('name')
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => $states,
            'message' => 'Jerarquía de ubicaciones obtenida exitosamente'
        ]);
    });
});

/*
|--------------------------------------------------------------------------
| Electoral Data API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('electoral')->group(function () {
    // Consulta por cédula (simulada)
    Route::get('/citizen/{cedula}', function ($cedula) {
        // Simular consulta de centro de votación por cédula
        $votingCenter = \App\Models\VotingCenter::with(['state', 'municipality', 'parish'])
            ->inRandomOrder()
            ->first();
            
        if (!$votingCenter) {
            return response()->json([
                'success' => false,
                'message' => 'No se encontraron centros de votación'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'cedula' => $cedula,
                'voting_center' => $votingCenter,
                'table_number' => rand(1, $votingCenter->total_tables ?: 1),
                'electoral_status' => 'active'
            ],
            'message' => 'Información electoral encontrada'
        ]);
    });
    
    // Centros cercanos por coordenadas
    Route::get('/nearby', function (Request $request) {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:50'
        ]);
        
        $lat = $request->latitude;
        $lng = $request->longitude;
        $radius = $request->get('radius', 5); // 5km por defecto
        
        // Fórmula de Haversine para encontrar centros cercanos
        $centers = \App\Models\VotingCenter::selectRaw("
            *,
            (6371 * acos(
                cos(radians(?)) * cos(radians(latitude)) *
                cos(radians(longitude) - radians(?)) +
                sin(radians(?)) * sin(radians(latitude))
            )) AS distance
        ", [$lat, $lng, $lat])
        ->whereNotNull('latitude')
        ->whereNotNull('longitude')
        ->having('distance', '<', $radius)
        ->orderBy('distance')
        ->with(['state', 'municipality', 'parish'])
        ->limit(10)
        ->get();
        
        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación en un radio de {$radius}km"
        ]);
    });
});

/*
|--------------------------------------------------------------------------
| Health Check Routes
|--------------------------------------------------------------------------
*/

Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'version' => '1.0.0',
        'database' => [
            'states' => \App\Models\State::count(),
            'municipalities' => \App\Models\Municipality::count(),
            'parishes' => \App\Models\Parish::count(),
            'voting_centers' => \App\Models\VotingCenter::count(),
        ]
    ]);
});

Route::get('/ping', function () {
    return response()->json(['message' => 'pong']);
});
