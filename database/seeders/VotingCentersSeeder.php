<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

class VotingCentersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Obtener IDs de ubicaciones para mapear correctamente
        $stateIds = State::pluck('id', 'code')->toArray();
        $municipalityIds = Municipality::pluck('id', 'code')->toArray();
        $parishIds = Parish::pluck('id', 'code')->toArray();

        // Datos reales de centros de votación basados en información del CNE 2024
        $votingCenters = [
            // DISTRITO CAPITAL - LIBERTADOR
            [
                'code' => '010101001',
                'name' => 'UNIDAD EDUCATIVA DISTRITAL MADARIAGA',
                'address' => 'PARROQUIA ALTAGRACIA, CARACAS',
                'state_code' => 'DC',
                'municipality_code' => 'LIB',
                'parish_code' => 'ALT',
                'total_voters' => 2272,
                'total_tables' => 3,
            ],
            [
                'code' => '010101002',
                'name' => 'UNIDAD EDUCATIVA SANTA BARBARA',
                'address' => 'PARROQUIA ALTAGRACIA, CARACAS',
                'state_code' => 'DC',
                'municipality_code' => 'LIB',
                'parish_code' => 'ALT',
                'total_voters' => 2317,
                'total_tables' => 3,
            ],
            [
                'code' => '010101003',
                'name' => 'UNIDAD EDUCATIVA PRIVADA COLEGIO LA SALLE',
                'address' => 'PARROQUIA ALTAGRACIA, CARACAS',
                'state_code' => 'DC',
                'municipality_code' => 'LIB',
                'parish_code' => 'ALT',
                'total_voters' => 6900,
                'total_tables' => 7,
            ],
            [
                'code' => '010102004',
                'name' => 'ESCUELA MUNICIPAL JOSÉ RAMÓN CAMEJO',
                'address' => 'PARROQUIA CANDELARIA, CARACAS',
                'state_code' => 'DC',
                'municipality_code' => 'LIB',
                'parish_code' => 'CAN',
                'total_voters' => 1663,
                'total_tables' => 2,
            ],
            [
                'code' => '010110032',
                'name' => 'ESCUELA BÁSICA NACIONAL BOLIVARIANA JOSÉ FLORENCIO JIMÉNEZ',
                'address' => 'PARROQUIA SUCRE, CARACAS',
                'state_code' => 'DC',
                'municipality_code' => 'LIB',
                'parish_code' => 'SUC',
                'total_voters' => 3071,
                'total_tables' => 4,
            ],

            // MIRANDA
            [
                'code' => '130101001',
                'name' => 'UNIDAD EDUCATIVA ROSCIO',
                'address' => 'CAUCAGUA, ESTADO MIRANDA',
                'state_code' => 'MIR',
                'municipality_code' => 'ACE',
                'parish_code' => 'CAU',
                'total_voters' => 4198,
                'total_tables' => 5,
            ],
            [
                'code' => '130901107',
                'name' => 'ESCUELA MUNICIPAL 5 DE JULIO',
                'address' => 'PETARE, ESTADO MIRANDA',
                'state_code' => 'MIR',
                'municipality_code' => 'SUC',
                'parish_code' => 'PET',
                'total_voters' => 3722,
                'total_tables' => 4,
            ],
            [
                'code' => '131603011',
                'name' => 'COLEGIO DE FARMACEUTICOS DISTRITO METROPOLITANO DE CARACAS Y EL ESTADO MIRANDA',
                'address' => 'LAS MINAS DE BARUTA, ESTADO MIRANDA',
                'state_code' => 'MIR',
                'municipality_code' => 'BAR',
                'parish_code' => 'LMI',
                'total_voters' => 1289,
                'total_tables' => 2,
            ],

            // ZULIA
            [
                'code' => '210516014',
                'name' => 'UNIDAD EDUCATIVA GABRIEL BRACHO',
                'address' => 'VENANCIO PULGAR, MARACAIBO, ZULIA',
                'state_code' => 'ZUL',
                'municipality_code' => 'MAC',
                'parish_code' => 'VPU',
                'total_voters' => 1267,
                'total_tables' => 2,
            ],
            [
                'code' => '210701016',
                'name' => 'UNIDAD EDUCATIVA ESTADAL AMERICA LOPEZ DE VARGAS',
                'address' => 'GUAJIRA, ZULIA',
                'state_code' => 'ZUL',
                'municipality_code' => 'GUA',
                'parish_code' => 'GUA',
                'total_voters' => 2936,
                'total_tables' => 3,
            ],
            [
                'code' => '211803009',
                'name' => 'ESCUELA BASICA BICENTENARIO JOSE ANTONIO PAEZ',
                'address' => 'SAN FRANCISCO, ZULIA',
                'state_code' => 'ZUL',
                'municipality_code' => 'SFR',
                'parish_code' => 'SFR',
                'total_voters' => 3685,
                'total_tables' => 4,
            ],

            // CARABOBO
            [
                'code' => '070301012',
                'name' => 'UNIDAD EDUCATIVA NUESTRA SEÑORA DEL CAMINO',
                'address' => 'MARIARA, CARABOBO',
                'state_code' => 'CAR',
                'municipality_code' => 'DIB',
                'parish_code' => 'MAR',
                'total_voters' => 3415,
                'total_tables' => 4,
            ],
            [
                'code' => '070401012',
                'name' => 'GRUPO ESCOLAR DIEGO IBARRA',
                'address' => 'GUACARA, CARABOBO',
                'state_code' => 'CAR',
                'municipality_code' => 'GUA',
                'parish_code' => 'GUA',
                'total_voters' => 4970,
                'total_tables' => 5,
            ],
            [
                'code' => '070905001',
                'name' => 'GRUPO ESCOLAR EDUARDO VISO',
                'address' => 'SAN BLAS, VALENCIA, CARABOBO',
                'state_code' => 'CAR',
                'municipality_code' => 'VAL',
                'parish_code' => 'SBL',
                'total_voters' => 3692,
                'total_tables' => 4,
            ],

            // LARA
            [
                'code' => '110201022',
                'name' => 'UNIDAD EDUCATIVA PABLO ACOSTA ORTIZ',
                'address' => 'CATEDRAL, BARQUISIMETO, LARA',
                'state_code' => 'LAR',
                'municipality_code' => 'IRI',
                'parish_code' => 'CAT3',
                'total_voters' => 3102,
                'total_tables' => 4,
            ],
            [
                'code' => '110201025',
                'name' => 'ESCUELA DE ARTES PLASTICAS MARTIN TOVAR Y TOVAR',
                'address' => 'CATEDRAL, BARQUISIMETO, LARA',
                'state_code' => 'LAR',
                'municipality_code' => 'IRI',
                'parish_code' => 'CAT3',
                'total_voters' => 1339,
                'total_tables' => 2,
            ],

            // MÉRIDA
            [
                'code' => '120801004',
                'name' => 'UNIDAD EDUCATIVA LA PUEBLITA',
                'address' => 'ARIAS, MÉRIDA',
                'state_code' => 'MER',
                'municipality_code' => 'LIB',
                'parish_code' => 'ARI4',
                'total_voters' => 748,
                'total_tables' => 1,
            ],
            [
                'code' => '121804001',
                'name' => 'GRUPO ESCOLAR ESTADO PORTUGUESA',
                'address' => 'SAN JUAN, MÉRIDA',
                'state_code' => 'MER',
                'municipality_code' => 'SUC',
                'parish_code' => 'SJU3',
                'total_voters' => 4496,
                'total_tables' => 5,
            ],

            // LA GUAIRA
            [
                'code' => '240101002',
                'name' => 'UNIDAD EDUCATIVA NACIONAL CORAPAL',
                'address' => 'CARABALLEDA, LA GUAIRA',
                'state_code' => 'LGU',
                'municipality_code' => 'VAR',
                'parish_code' => 'CAR2',
                'total_voters' => 4580,
                'total_tables' => 5,
            ],
            [
                'code' => '240106009',
                'name' => 'UNIDAD EDUCATIVA PRIVADA BLAISE PASCAL',
                'address' => 'MACUTO, LA GUAIRA',
                'state_code' => 'LGU',
                'municipality_code' => 'VAR',
                'parish_code' => 'MAC2',
                'total_voters' => 1865,
                'total_tables' => 2,
            ],

            // APURE
            [
                'code' => '030101030',
                'name' => 'NUCLEO EL ESFUERZO',
                'address' => 'ACHAGUAS, APURE',
                'state_code' => 'APU',
                'municipality_code' => 'ACH',
                'parish_code' => 'ACH',
                'total_voters' => 389,
                'total_tables' => 1,
            ],

            // BARINAS
            [
                'code' => '050702013',
                'name' => 'JARDIN DE INFANCIA KIKIRIKI',
                'address' => 'LIBERTAD, ROJAS, BARINAS',
                'state_code' => 'BAR',
                'municipality_code' => 'ROJ',
                'parish_code' => 'LIB2',
                'total_voters' => 222,
                'total_tables' => 1,
            ],
            [
                'code' => '050803001',
                'name' => 'UNIDAD EDUCATIVA NACIONAL BOLIVARIANA RODRIGUEZ DOMINGUEZ',
                'address' => 'PUERTO DE NUTRIAS, SOSA, BARINAS',
                'state_code' => 'BAR',
                'municipality_code' => 'SOS',
                'parish_code' => 'PNU2',
                'total_voters' => 1924,
                'total_tables' => 2,
            ],

            // BOLÍVAR
            [
                'code' => '060108008',
                'name' => 'ESCUELA ESTADAL UNITARIA 282',
                'address' => 'UNARE, CARONI, BOLIVAR',
                'state_code' => 'BOL',
                'municipality_code' => 'CAR',
                'parish_code' => 'UNA',
                'total_voters' => 1709,
                'total_tables' => 2,
            ],

            // GUÁRICO
            [
                'code' => '100601006',
                'name' => 'ESCUELA BASICA JUAN ANTONIO PADILLA',
                'address' => 'SAN JUAN DE LOS MORROS, GUARICO',
                'state_code' => 'GUA',
                'municipality_code' => 'JGE',
                'parish_code' => 'SJM2',
                'total_voters' => 2541,
                'total_tables' => 3,
            ],

            // TRUJILLO
            [
                'code' => '191602004',
                'name' => 'UNIDAD BÁSICA EDUCATIVA MONSEÑOR CARRILLO',
                'address' => 'CHEREGUE, BOLIVAR, TRUJILLO',
                'state_code' => 'TRU',
                'municipality_code' => 'BOL',
                'parish_code' => 'CHE2',
                'total_voters' => 1325,
                'total_tables' => 2,
            ],
        ];

        foreach ($votingCenters as $center) {
            // Buscar IDs de ubicación
            $stateId = $this->findStateId($center['state_code'], $stateIds);
            $municipalityId = $this->findMunicipalityId($center['municipality_code'], $municipalityIds);
            $parishId = $this->findParishId($center['parish_code'], $parishIds);

            if ($stateId && $municipalityId && $parishId) {
                VotingCenter::create([
                    'code' => $center['code'],
                    'name' => $center['name'],
                    'address' => $center['address'],
                    'state_id' => $stateId,
                    'municipality_id' => $municipalityId,
                    'parish_id' => $parishId,
                    'total_voters' => $center['total_voters'],
                    'total_tables' => $center['total_tables'],
                    'status' => 'active',
                ]);
            }
        }
    }

    private function findStateId($code, $stateIds)
    {
        return $stateIds[$code] ?? null;
    }

    private function findMunicipalityId($code, $municipalityIds)
    {
        return $municipalityIds[$code] ?? null;
    }

    private function findParishId($code, $parishIds)
    {
        return $parishIds[$code] ?? null;
    }
}
