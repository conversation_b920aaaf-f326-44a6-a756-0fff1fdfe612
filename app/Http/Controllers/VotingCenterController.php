<?php

namespace App\Http\Controllers;

use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class VotingCenterController extends Controller
{
    /**
     * Display a listing of voting centers
     */
    public function index(Request $request): JsonResponse
    {
        $query = VotingCenter::with(['state', 'municipality', 'parish']);

        // Filtros
        if ($request->has('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        if ($request->has('municipality_id')) {
            $query->where('municipality_id', $request->municipality_id);
        }

        if ($request->has('parish_id')) {
            $query->where('parish_id', $request->parish_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%");
            });
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginación
        $perPage = $request->get('per_page', 15);
        $centers = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => 'Centros de votación obtenidos exitosamente'
        ]);
    }

    /**
     * Store a newly created voting center
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|max:20|unique:voting_centers',
            'name' => 'required|string|max:255',
            'address' => 'nullable|string',
            'state_id' => 'required|exists:states,id',
            'municipality_id' => 'required|exists:municipalities,id',
            'parish_id' => 'required|exists:parishes,id',
            'total_voters' => 'nullable|integer|min:0',
            'total_tables' => 'nullable|integer|min:0',
            'status' => 'nullable|in:active,inactive,suspended',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        $center = VotingCenter::create($request->all());

        return response()->json([
            'success' => true,
            'data' => $center->load(['state', 'municipality', 'parish']),
            'message' => 'Centro de votación creado exitosamente'
        ], 201);
    }

    /**
     * Display the specified voting center
     */
    public function show(VotingCenter $votingCenter): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $votingCenter->load(['state', 'municipality', 'parish']),
            'message' => 'Centro de votación obtenido exitosamente'
        ]);
    }

    /**
     * Update the specified voting center
     */
    public function update(Request $request, VotingCenter $votingCenter): JsonResponse
    {
        $request->validate([
            'code' => 'sometimes|string|max:20|unique:voting_centers,code,' . $votingCenter->id,
            'name' => 'sometimes|string|max:255',
            'address' => 'nullable|string',
            'state_id' => 'sometimes|exists:states,id',
            'municipality_id' => 'sometimes|exists:municipalities,id',
            'parish_id' => 'sometimes|exists:parishes,id',
            'total_voters' => 'nullable|integer|min:0',
            'total_tables' => 'nullable|integer|min:0',
            'status' => 'nullable|in:active,inactive,suspended',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        $votingCenter->update($request->all());

        return response()->json([
            'success' => true,
            'data' => $votingCenter->load(['state', 'municipality', 'parish']),
            'message' => 'Centro de votación actualizado exitosamente'
        ]);
    }

    /**
     * Remove the specified voting center
     */
    public function destroy(VotingCenter $votingCenter): JsonResponse
    {
        $votingCenter->delete();

        return response()->json([
            'success' => true,
            'message' => 'Centro de votación eliminado exitosamente'
        ]);
    }

    /**
     * Get voting centers by state
     */
    public function byState(State $state): JsonResponse
    {
        $centers = VotingCenter::where('state_id', $state->id)
            ->with(['municipality', 'parish'])
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación del estado {$state->name}"
        ]);
    }

    /**
     * Get voting centers by municipality
     */
    public function byMunicipality(Municipality $municipality): JsonResponse
    {
        $centers = VotingCenter::where('municipality_id', $municipality->id)
            ->with(['state', 'parish'])
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación del municipio {$municipality->name}"
        ]);
    }

    /**
     * Get voting centers by parish
     */
    public function byParish(Parish $parish): JsonResponse
    {
        $centers = VotingCenter::where('parish_id', $parish->id)
            ->with(['state', 'municipality'])
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $centers,
            'message' => "Centros de votación de la parroquia {$parish->name}"
        ]);
    }

    /**
     * Search voting centers by code
     */
    public function searchByCode(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $center = VotingCenter::where('code', $request->code)
            ->with(['state', 'municipality', 'parish'])
            ->first();

        if (!$center) {
            return response()->json([
                'success' => false,
                'message' => 'Centro de votación no encontrado'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $center,
            'message' => 'Centro de votación encontrado'
        ]);
    }

    /**
     * Get voting centers statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_centers' => VotingCenter::count(),
            'active_centers' => VotingCenter::where('status', 'active')->count(),
            'inactive_centers' => VotingCenter::where('status', 'inactive')->count(),
            'suspended_centers' => VotingCenter::where('status', 'suspended')->count(),
            'total_voters' => VotingCenter::sum('total_voters'),
            'total_tables' => VotingCenter::sum('total_tables'),
            'centers_by_state' => VotingCenter::join('states', 'voting_centers.state_id', '=', 'states.id')
                ->selectRaw('states.name as state_name, COUNT(*) as total')
                ->groupBy('states.id', 'states.name')
                ->orderBy('total', 'desc')
                ->get(),
            'centers_with_coordinates' => VotingCenter::whereNotNull('latitude')
                ->whereNotNull('longitude')
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Estadísticas de centros de votación'
        ]);
    }

    /**
     * Export voting centers data
     */
    public function export(Request $request): JsonResponse
    {
        $format = $request->get('format', 'json');
        
        $centers = VotingCenter::with(['state', 'municipality', 'parish'])
            ->active()
            ->get();

        switch ($format) {
            case 'csv':
                // Implementar exportación CSV
                return response()->json([
                    'success' => false,
                    'message' => 'Exportación CSV en desarrollo'
                ]);
                
            case 'excel':
                // Implementar exportación Excel
                return response()->json([
                    'success' => false,
                    'message' => 'Exportación Excel en desarrollo'
                ]);
                
            default:
                return response()->json([
                    'success' => true,
                    'data' => $centers,
                    'message' => 'Datos de centros de votación exportados'
                ]);
        }
    }
}
